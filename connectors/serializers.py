from rest_framework import serializers
from django.utils import timezone
from .models import LineChannel, LineChannelAudit
from .connection.services import LineConnectionService


class LineChannelAuditSerializer(serializers.ModelSerializer):
    """Serializer for LINE channel audit logs."""
    performed_by_name = serializers.CharField(source='performed_by.username', read_only=True)
    
    class Meta:
        model = LineChannelAudit
        fields = [
            'id', 'action', 'changes', 'performed_by', 'performed_by_name',
            'performed_at', 'reason'
        ]
        read_only_fields = ['id', 'performed_at']


class LineChannelListSerializer(serializers.ModelSerializer):
    """Serializer for LINE channel list view."""
    webhook_url = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = LineChannel
        fields = [
            'id', 'name', 'channel_id', 'provider_id', 'provider_name', 'status', 
            'is_active', 'webhook_verified', 'webhook_url', 'created_on', 'created_by_name'
        ]
        read_only_fields = fields

    def get_webhook_url(self, obj):
        """Generate the webhook URL for this channel."""
        from django.conf import settings
        return f"{settings.PUBLIC_BACKEND_URL}/connectors/webhook/line/{obj.channel_id}/"

class LineChannelDetailSerializer(serializers.ModelSerializer):
    """Serializer for LINE channel detail view."""
    webhook_url = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    updated_by_name = serializers.CharField(source='updated_by.username', read_only=True)
    deleted_by_name = serializers.CharField(source='deleted_by.username', read_only=True)
    recent_audits = serializers.SerializerMethodField()
    
    class Meta:
        model = LineChannel
        fields = [
            'id', 'name', 'channel_id', 'channel_secret', 'channel_access_token',
            'webhook_verified', 'webhook_url',
            'provider_id', 'provider_name', 
            # 'line_provider_id',
            'status', 'is_active', 'status_changed_at', 'status_reason',
            'is_deleted', 'deleted_at', 'deleted_by', 'deleted_by_name',
            'created_by', 'created_by_name', 'created_on',
            'updated_by', 'updated_by_name', 'updated_on',
            'recent_audits'
        ]
        read_only_fields = [
            'id', 'channel_id', 'webhook_verified', 'status_changed_at',
            'is_deleted', 'deleted_at', 'deleted_by', 'created_on', 'updated_on',
            'recent_audits'
        ]
    
    def get_recent_audits(self, obj):
        """Get recent audit logs for this channel."""
        recent_audits = obj.audit_logs.all()[:5]
        return LineChannelAuditSerializer(recent_audits, many=True).data
    
    def get_webhook_url(self, obj):
        """Generate the webhook URL for this channel."""
        from django.conf import settings
        return f"{settings.PUBLIC_BACKEND_URL}/connectors/webhook/line/{obj.channel_id}/"


class LineChannelCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating LINE channels."""
    
    class Meta:
        model = LineChannel
        fields = [
            'name', 'channel_id', 'channel_secret', 'channel_access_token',
            'provider_id', 'provider_name', 
            # 'line_provider_id'
        ]
        
    def validate(self, data):
        """Validate channel credentials before creation."""
        # Check if channel already exists
        if LineChannel.objects.filter(channel_id=data['channel_id']).exists():
            raise serializers.ValidationError({
                'channel_id': 'Channel with this ID already exists.'
            })
        
        # Validate credentials using the service
        service = LineConnectionService()
        validation_result = service.validate_credentials({
            'channel_id': data['channel_id'],
            'channel_secret': data['channel_secret'],
            'channel_access_token': data['channel_access_token']
        })
        
        if not validation_result.get('success', False):
            raise serializers.ValidationError({
                'channel_access_token': validation_result.get('error', 'Invalid credentials')
            })
            
        return data
    
    def create(self, validated_data):
        """Create LINE channel with audit log."""
        user = self.context['request'].user
        validated_data['created_by'] = user
        
        channel = super().create(validated_data)
        
        # Create audit log
        LineChannelAudit.objects.create(
            channel=channel,
            action='created',
            changes=validated_data,
            performed_by=user
        )
        
        return channel


class LineChannelUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating LINE channels."""
    
    class Meta:
        model = LineChannel
        fields = [
            'name', 'channel_secret', 'channel_access_token',
            'provider_id', 'provider_name', 
            # 'line_provider_id'
        ]
        
    def validate(self, data):
        """Validate updated credentials if changed."""
        if 'channel_access_token' in data or 'channel_secret' in data:
            # Get current channel data
            channel = self.instance
            
            # Prepare validation data
            validation_data = {
                'channel_id': channel.channel_id,
                'channel_secret': data.get('channel_secret', channel.channel_secret),
                'channel_access_token': data.get('channel_access_token', channel.channel_access_token)
            }
            
            # Validate credentials
            service = LineConnectionService()
            validation_result = service.validate_credentials(validation_data)
            
            if not validation_result.get('success', False):
                raise serializers.ValidationError({
                    'channel_access_token': validation_result.get('error', 'Invalid credentials')
                })
                
        return data
    
    def update(self, instance, validated_data):
        """Update channel with audit log."""
        user = self.context['request'].user
        
        # Track changes
        changes = {}
        for field, new_value in validated_data.items():
            old_value = getattr(instance, field)
            if old_value != new_value:
                changes[field] = {
                    'old': old_value,
                    'new': new_value
                }
        
        # Update fields
        validated_data['updated_by'] = user
        channel = super().update(instance, validated_data)
        
        # Create audit log if there were changes
        if changes:
            LineChannelAudit.objects.create(
                channel=channel,
                action='updated',
                changes=changes,
                performed_by=user
            )
        
        return channel


class LineChannelStatusSerializer(serializers.Serializer):
    """Serializer for changing channel status."""
    action = serializers.ChoiceField(choices=['enable', 'disable'])
    reason = serializers.CharField(required=False, allow_blank=True)
    
    def save(self):
        """Change channel status."""
        channel = self.context['channel']
        user = self.context['request'].user
        action = self.validated_data['action']
        reason = self.validated_data.get('reason', '')
        
        if action == 'enable':
            channel.enable(reason=reason, user=user)
        else:
            channel.disable(reason=reason, user=user)
            
        return channel


class LineChannelSoftDeleteSerializer(serializers.Serializer):
    """Serializer for soft deleting a channel."""
    reason = serializers.CharField(required=False, allow_blank=True)
    
    def save(self):
        """Perform soft delete."""
        channel = self.context['channel']
        user = self.context['request'].user
        reason = self.validated_data.get('reason', '')
        
        # Perform soft delete
        channel.soft_delete(user=user)
        
        # Create audit log
        LineChannelAudit.objects.create(
            channel=channel,
            action='deleted',
            changes={'is_deleted': True, 'deleted_at': str(timezone.now())},
            performed_by=user,
            reason=reason
        )
        
        return channel