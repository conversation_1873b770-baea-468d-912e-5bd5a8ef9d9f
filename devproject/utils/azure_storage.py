import logging
from azure.storage.blob import BlobServiceClient
from django.conf import settings
from devproject.utils.utils import bcolors

from io import BytesIO

logger = logging.getLogger('django.chatbot_logs')

class AzureBlobStorage:
    def __init__(self):
        connection_string = f"DefaultEndpointsProtocol=https;AccountName={settings.AZURE_ACCOUNT_NAME};AccountKey={settings.AZURE_ACCOUNT_KEY};EndpointSuffix=core.windows.net"
        self.blob_service_client = BlobServiceClient.from_connection_string(connection_string)
        self.container_client = self.blob_service_client.get_container_client(settings.AZURE_CONTAINER)



        # Check container access level when initializing
        self.ensure_public_access()

    def ensure_public_access(self):
        """
        Check and optionally set container public access level.
        Returns the current access level.
        """
        try:
            container_properties = self.container_client.get_container_properties()
            current_access = container_properties.public_access
            
            if current_access is None:
                print("Warning: Container does not have public access configured")
                # Uncomment the following lines if you want to automatically set public access
                # print("Setting container to public blob access...")
                # self.container_client.set_container_access_policy(public_access=PublicAccess.Blob)
                # print("Container public access configured successfully")
            else:
                print(f"Container public access level: {current_access}")
                
            return current_access
            
        except Exception as e:
            print(f"Error checking/setting container access: {str(e)}")
            return None












    # TODO - Delete this comment
    # This version is work but uploaded files get corrupted
    def upload_file(self, file, blob_name):
        """
        Upload a file to Azure Blob Storage
        
        Args:
            file: File object (e.g., from request.FILES)
            blob_name: Name to give the blob in storage
            
        Returns:
            URL of the uploaded blob
        """
        try:

            # TODO - Delete this
            print(f"upload_file's type of file - {type(file)}")
            
            blob_client = self.container_client.get_blob_client(blob_name)
            blob_client.upload_blob(file.read(), overwrite=True)
            return blob_client.url
            


    #         # TODO - Delete this
    #         # Idea - Check file whether it is correct format for uploading Azure Blob
    #         # Try - codes and direct upload on Azure Blob service
    #         # Result - Probably because how API request interact with"file" key as File type
    #         # Uploaded files are not corrupted when I tried to direct upload on Azure Blob service

    #         # print(f"upload_file's type of file - {type(file)}")
    #         # print(f"upload_file's type of file.read() - {type(file.read())}")
    #         # file.seek(0)
    #         # blob_client.upload_blob(file, overwrite=True)

    #         # TODO - Delete this
    #         # Idea - 
    #         # Ref - https://learn.microsoft.com/en-us/azure/storage/blobs/storage-blob-upload-python (Upload a block blob from a local file path)
    #         # Result -

    #         # filename = file.name
    #         # blob_client = self.container_client.upload_blob(name=filename, data=file.read(), overwrite=True)

    #         # with open(file=file, mode="rb") as data:
    #         #     filename = file.name
    #         #     print(f"upload_file's data - {data}")
    #         #     print(f"upload_file's type of data - {type(data)}")
    #         #     blob_client = self.container_client.upload_blob(name=filename, data=data, overwrite=True)

        except Exception as e:
            raise Exception(f"Error uploading file to Azure Blob Storage: {str(e)}")

    # # 2nd version
    # # Idea - From guessing "how API request interact with"file" key as File type", load file here directly
    # # Ref - https://learn.microsoft.com/en-us/azure/storage/blobs/storage-blob-upload-python (Upload a block blob from a local file path)
    # # Result - This code is not corrupt uploaded files
    # def upload_file(self, file, blob_name):
    #     """
    #     Upload a file to Azure Blob Storage
        
    #     Args:
    #         file: File object (e.g., from request.FILES)
    #         blob_name: Name to give the blob in storage
            
    #     Returns:
    #         URL of the uploaded blob
    #     """
    #     try:
    #         blob_client = self.container_client.get_blob_client(blob_name)
            
    #         # TODO - Delete this
    #         print(f"upload_file's type of file - {type(file)}")
    #         filename = file.name
    #         # with open(file=f"/workspaces/Salmate/TPB Document/Templates - Upload/{filename}", mode="rb") as data:
    #         with open(file=f"/workspaces/Salmate/llm_rag_doc/tests_files/{filename}", mode="rb") as data:
    #             blob_client = self.container_client.upload_blob(name=filename, data=data, overwrite=True)

    #     except Exception as e:
    #         raise Exception(f"Error uploading file to Azure Blob Storage: {str(e)}")



    # # 3rd version
    # # Result - Uploaded files are still corrupted
    # def upload_file(self, file, blob_name):
    #     """
    #     Upload a file to Azure Blob Storage with proper binary handling
        
    #     Args:
    #         file: File object (e.g., from request.FILES)
    #         blob_name: Name to give the blob in storage
            
    #     Returns:
    #         URL of the uploaded blob
    #     """
    #     try:
    #         blob_client = self.container_client.get_blob_client(blob_name)
            
    #         # For small files
    #         if hasattr(file, 'read'):
    #             # Reset file pointer to beginning
    #             file.seek(0)
    #             # Read as binary
    #             data = file.read()
    #             # Upload with content_type
    #             blob_client.upload_blob(
    #                 data,
    #                 overwrite=True,
    #                 content_type=file.content_type
    #             )
    #         # For large files, use chunks
    #         else:
    #             chunks = []
    #             for chunk in file.chunks():
    #                 chunks.append(chunk)
                
    #             blob_client.upload_blob(
    #                 b''.join(chunks),
    #                 overwrite=True,
    #                 content_type=file.content_type
    #             )
                
    #         return blob_client.url
    #     except Exception as e:
    #         print(f"Upload error details: {str(e)}")
    #         raise Exception(f"Error uploading file to Azure Blob Storage: {str(e)}")









    # # 4th version
    # Result - Uploaded files are still corrupted
    # def upload_file(self, file, blob_name):
    #     try:
    #         blob_client = self.container_client.get_blob_client(blob_name)
            
    #         # Use BytesIO buffer
    #         buffer = BytesIO()
    #         for chunk in file.chunks():
    #             buffer.write(chunk)
                
    #         # Reset buffer position
    #         buffer.seek(0)
            
    #         # Upload from buffer
    #         blob_client.upload_blob(
    #             buffer.getvalue(),
    #             overwrite=True,
    #             content_type=file.content_type
    #         )
            
    #         return blob_client.url
    #     except Exception as e:
    #         print(f"Upload error details: {str(e)}")
    #         raise Exception(f"Error uploading file to Azure Blob Storage: {str(e)}")



















    def delete_file(self, blob_name):
        """
        Delete a file from Azure Blob Storage
        
        Args:
            blob_name: Name of the blob to delete
            
        Returns:
            Boolean indicating success
        """
        try:
            blob_client = self.container_client.get_blob_client(blob_name)
            blob_client.delete_blob()
            return True
        except Exception as e:
            raise Exception(f"Error deleting file from Azure Blob Storage: {str(e)}")

    def get_file_url(self, blob_name):
        """
        Get the URL for a file in Azure Blob Storage
        
        Args:
            blob_name: Name of the blob
            
        Returns:
            URL of the blob
        """
        blob_client = self.container_client.get_blob_client(blob_name)
        return blob_client.url

    # Add SAS token to get_file_url function (For image temporary)
    def get_file_url_image(self, blob_name):
        """
        Get the URL for a file in Azure Blob Storage with SAS token
        
        Args:
            blob_name: Name of the blob
            
        Returns:
            URL of the blob with SAS token
        """
        try:
            blob_client = self.container_client.get_blob_client(blob_name)
            
            # Generate SAS token that expires in 1 hour
            from datetime import datetime, timedelta
            from azure.storage.blob import generate_blob_sas, BlobSasPermissions
            
            sas_token = generate_blob_sas(
                account_name=self.blob_service_client.account_name,
                container_name=self.container_client.container_name,
                blob_name=blob_name,
                account_key=self.blob_service_client.credential.account_key,
                permission=BlobSasPermissions(read=True),
                expiry=datetime.utcnow() + timedelta(hours=1)
            )
            
            # Return URL with SAS token
            return f"{blob_client.url}?{sas_token}"
        except Exception as e:
            raise Exception(f"Error getting file URL: {str(e)}")


    def get_file_url_image_v2(self, blob_name, sas_lifetime_days=365):
        """
        Get the URL for a file in Azure Blob Storage with SAS token
        
        Args:
            blob_name: Name of the blob
            
        Returns:
            URL of the blob with SAS token
        """
        try:
            # First verify the blob exists
            blob_client = self.container_client.get_blob_client(blob_name)
            try:
                blob_client.get_blob_properties()
            except Exception as e:
                raise Exception(f"Blob does not exist: {blob_name}")

            # Generate SAS token
            from datetime import datetime, timedelta
            from azure.storage.blob import generate_blob_sas, BlobSasPermissions

            # Define start and expiry time
            start_time = datetime.utcnow()
            # expiry_time = start_time + timedelta(days=30) # SAS's lifetime
            expiry_time = start_time + timedelta(days=sas_lifetime_days) # SAS's lifetime

            # Generate SAS token with explicit permissions
            sas_token = generate_blob_sas(
                account_name=self.blob_service_client.account_name,
                container_name=self.container_client.container_name,
                blob_name=blob_name,
                account_key=self.blob_service_client.credential.account_key,
                permission=BlobSasPermissions(read=True),
                start=start_time,
                expiry=expiry_time,
                protocol="https"  # Force HTTPS
            )
            
            # Build the full URL
            sas_url = f"{blob_client.url}?{sas_token}"

            # TODO - Delete this or Log this
            logger.info(f"get_file_url_image_v2's blob_client.url : {blob_client.url}")
            logger.info(f"get_file_url_image_v2's sas_url : {sas_url}")
            print(f"{bcolors.HEADER}|----get_file_url_image_v2's blob_client.url : {blob_client.url}{bcolors.ENDC}")
            print(f"{bcolors.HEADER}|----get_file_url_image_v2's sas_url : {sas_url}{bcolors.ENDC}")
            
            # Verify the URL is accessible
            import requests
            response = requests.head(sas_url)
            if response.status_code != 200:
                raise Exception(f"URL verification failed with status code: {response.status_code}")
                
            print(f"Successfully generated SAS URL for blob: {blob_name}")
            return sas_url

        except Exception as e:
            raise Exception(f"Error generating SAS URL: {str(e)}")















    def list_files(self, prefix):
        """List all files with the given prefix"""
        try:
            files = []
            blobs = self.container_client.list_blobs(name_starts_with=prefix)
            for blob in blobs:
                files.append({
                    'name': blob.name.replace(prefix, ''),  # Remove prefix to get just filename
                    'size': blob.size,
                    'created_on': blob.creation_time,
                    'url': f"{self.container_client.url}/{blob.name}"
                })
            return files
        except Exception as e:
            raise Exception(f"Error listing files from Azure Blob Storage: {str(e)}")

    def download_file(self, blob_name):
        """
        Download a file from Azure Blob Storage
        
        Args:
            blob_name: Name/path of the blob to download
            
        Returns:
            BytesIO object containing the file data
        
        Raises:
            Exception: If there's an error downloading the file
        """
        try:
            # Get blob client
            blob_client = self.container_client.get_blob_client(blob_name)
            
            # Download blob content
            download_stream = blob_client.download_blob()
            
            # Create a BytesIO object to hold the image data
            file_stream = BytesIO()
            download_stream.readinto(file_stream)
            
            # Reset stream position to beginning
            file_stream.seek(0)
            
            return file_stream
        except Exception as e:
            raise Exception(f"Error downloading file from Azure Blob Storage: {str(e)}")

    def get_file_content_type(self, blob_name):
        """
        Get the content type of a file in Azure Blob Storage
        
        Args:
            blob_name: Name/path of the blob
            
        Returns:
            String containing the content type (e.g., 'image/jpeg')
        """
        try:
            blob_client = self.container_client.get_blob_client(blob_name)
            properties = blob_client.get_blob_properties()
            return properties.content_settings.content_type
        except Exception as e:
            raise Exception(f"Error getting file content type: {str(e)}")