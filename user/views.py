import io
import json
import zipfile
from datetime import datetime
from django.contrib.auth import authenticate
from django.shortcuts import get_object_or_404
from django.http import HttpResponse
from django.db import transaction
from django.db.models import Q, Count, Subquery, OuterRef, Prefetch
from django.utils import timezone
from rest_framework.pagination import PageNumberPagination
from django_filters import rest_framework as django_filters
from rest_framework import generics, mixins, status, viewsets, filters
from rest_framework.decorators import action
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet
from rest_framework.authtoken.models import Token
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.permissions import (
    IsAuthenticated,
    IsAuthenticatedOrReadOnly,
    IsAdminUser,
)
from rest_framework.filters import OrderingFilter

from user.permissions import <PERSON><PERSON><PERSON><PERSON>, IsOwnerOrAd<PERSON>, IsOwnerOrSupervisorOr<PERSON>igher, IsSuper<PERSON><PERSON><PERSON><PERSON>igh<PERSON>, TicketOwnershipPermission

from .models import User, Role, Permission, UserActivityLog, UserRole, RolePermission, Department, UserSchedule, UserTag
from .serializers import (
    PasswordChangeSerializer,
    UserResponseSerializer,
    UserSerializer, 
    RoleSerializer, 
    DepartmentSerializer,
    UserDepartmentSerializer,
    PermissionSerializer, 
    UserRoleSerializer, 
    RolePermissionSerializer, 
    LoginSerializer, 
    LogoutSerializer, 
    UserDeleteSerializer, 
    RoleAssignmentSerializer,
    UserSignUpSerializer,
    UserStatusResponseSerializer,
    UserTagBasicSerializer,
    UserTagSerializer,
    UserToggleStatusSerializer,
    UserUpdateSerializer, 
    UsersInfosSerializer,
    UserLineAccountUpdateSerializer
)

from .services import UserStatusService
from .utils.workload import check_user_workload
from ticket.models import Ticket, OwnerLog, Status
from ticket.serializers import EnhancedTicketSerializer, TicketSerializer
from llm_rag_doc.serializers import Company, UserCompanySerializer
from linechatbot.models import LineUserProfile


from devproject.utils.utils import LoggingMixin
from devproject.utils.azure_storage import AzureBlobStorage

import logging

logger = logging.getLogger('django.api_logs')

# from drf_yasg.utils import swagger_auto_schema
# from drf_yasg import openapi

# class SignUpView(LoggingMixin, generics.GenericAPIView):
#     serializer_class = UserSignUpSerializer
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated, IsSupervisorOrHigher]
    
#     def post(self, request: Request):
#         data = request.data
#         serializer = self.serializer_class(data=data, context={'request': request})
        
#         # Initialize metadata
#         metadata = {
#             'success': False,
#             'duplicated_username': False,
#             'duplicated_email': False,
#             'passwords_match': True,
#             'password_min_length_error': False,
#             'password_no_uppercase_error': False,
#             'password_no_lowercase_error': False,
#             'password_no_number_error': False,
#             'password_no_special_char_error': False,
#             'password_valid': True,
#             'missing_required_fields': False,
#             'invalid_role_ids': False
#         }
        
#         if serializer.is_valid():
#             # Update metadata with validation results
#             metadata.update(serializer.get_validation_metadata())
#             metadata['success'] = True
            
#             # Use database transaction to ensure atomicity
#             with transaction.atomic():
#                 # Save the user
#                 signup_user = serializer.save(created_by=request.user)
                
#                 # Assign roles to the user
#                 role_ids = data.get("role_ids", None)
#                 assigned_roles = []
#                 invalid_ids = []
                
#                 if role_ids is None:
#                     # Assign Default role as Agent
#                     try:
#                         role = Role.objects.get(name='Agent', is_active=True)
#                         UserRole.objects.create(
#                             user_id=signup_user,
#                             role_id=role,
#                             created_by=request.user
#                         )
#                         assigned_roles.append(role.name)
#                     except Role.DoesNotExist:
#                         self.log_warning(f"Default 'Agent' role not found for user {signup_user.username}")
#                 else:
#                     roles = []
                    
#                     # Extract valid roles
#                     for role_id in role_ids:
#                         try:
#                             role = Role.objects.get(role_id=role_id, is_active=True)
#                             roles.append(role)
#                         except Role.DoesNotExist:
#                             invalid_ids.append(role_id)
                    
#                     # Update metadata if invalid role IDs found
#                     if invalid_ids:
#                         metadata['invalid_role_ids'] = True
#                         self.log_warning(f"Invalid role IDs provided: {invalid_ids}")
                    
#                     # Assign valid roles to the user
#                     for role in roles:
#                         UserRole.objects.create(
#                             user_id=signup_user,
#                             role_id=role,
#                             created_by=request.user
#                         )
#                         assigned_roles.append(role.name)
                
#                 # Prepare response data
#                 response_data = {
#                     "message": f"User '{signup_user.username}' created successfully",
#                     "data": serializer.data,
#                     "assigned_roles": assigned_roles,
#                     "metadata": metadata
#                 }
                
#                 # Add warning about invalid role IDs if any
#                 if role_ids and invalid_ids:
#                     response_data["warning"] = f"Invalid role IDs ignored: {invalid_ids}"
                
#                 return Response(data=response_data, status=status.HTTP_201_CREATED)
#         else:
#             # Update metadata based on validation errors
#             metadata.update(serializer.get_validation_metadata() if hasattr(serializer, 'get_validation_metadata') else {})
            
#             # Check for missing required fields
#             for field in ['username', 'email', 'name', 'password', 'confirm_password']:
#                 if field in serializer.errors:
#                     for error in serializer.errors[field]:
#                         if 'required' in str(error).lower():
#                             metadata['missing_required_fields'] = True
#                             break
            
#             response_data = {
#                 "message": "Validation failed",
#                 "errors": serializer.errors,
#                 "metadata": metadata
#             }
            
#             return Response(data=response_data, status=status.HTTP_400_BAD_REQUEST)

class SignUpView(LoggingMixin, generics.GenericAPIView):
    serializer_class = UserSignUpSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated, IsSupervisorOrHigher]
    
    def post(self, request: Request):
        serializer = self.serializer_class(data=request.data, context={'request': request})
        
        # Initialize metadata
        metadata = {
            'success': False,
            'duplicated_username': False,
            'duplicated_work_email': False,
            'passwords_match': True,
            'password_min_length_error': False,
            'password_no_uppercase_error': False,
            'password_no_lowercase_error': False,
            'password_no_number_error': False,
            'password_no_special_char_error': False,
            'password_valid': True,
            'missing_required_fields': False
        }
        
        if serializer.is_valid():
            # Update metadata with validation results
            metadata.update(serializer.get_validation_metadata())
            metadata['success'] = True
            
            # Use database transaction to ensure atomicity
            with transaction.atomic():
                # Save the user
                signup_user = serializer.save(created_by=request.user)
                
                # Automatically assign Agent role
                try:
                    agent_role = Role.objects.get(name='Agent', is_active=True)
                    UserRole.objects.create(
                        user_id=signup_user,
                        role_id=agent_role,
                        created_by=request.user
                    )
                except Role.DoesNotExist:
                    self.log_warning(f"Default 'Agent' role not found for user {signup_user.username}")
                
                # Create UserSchedule for this new created user account with default value the same as business hours
                UserSchedule.objects.create(
                    user=signup_user,
                    same_as_business_hours=True,
                    schedule={}
                )
                
                # Use response serializer for consistent output
                response_serializer = UserResponseSerializer(signup_user)
                
                # Prepare standardized response
                response_data = {
                    "message": "User created successfully",
                    "data": response_serializer.data,
                    "metadata": metadata
                }
                
                return Response(data=response_data, status=status.HTTP_201_CREATED)
        else:
            # Update metadata based on validation errors
            metadata.update(serializer.get_validation_metadata() if hasattr(serializer, 'get_validation_metadata') else {})
            
            # Check for missing required fields
            for field in ['username', 'work_email', 'first_name', 'last_name', 'password', 'confirm_password']:
                if field in serializer.errors:
                    for error in serializer.errors[field]:
                        if 'required' in str(error).lower():
                            metadata['missing_required_fields'] = True
                            break
            
            response_data = {
                "message": "Validation failed",
                "errors": serializer.errors,
                "metadata": metadata
            }
            
            return Response(data=response_data, status=status.HTTP_400_BAD_REQUEST)

class LoginView(LoggingMixin, APIView):

    serializer_class = LoginSerializer
    # authentication_class = []
    permission_classes = []

    def post(self, request: Request):
        serializer = self.serializer_class(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        response = {"message": "Login Successfully", "data": serializer.data}
        return Response(data=response, status=status.HTTP_200_OK)

class LogoutView(LoggingMixin, generics.GenericAPIView):
    serializer_class = LogoutSerializer
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = self.serializer_class(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        response = {"message": "Logout Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)

class UserOwnInfo(generics.RetrieveAPIView):
    """View for getting current user's status"""
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        return self.request.user


# class PasswordChangeView(LoggingMixin, APIView):
#     """
#     API endpoint that allows users to change their password.
#     If you're an admin, you can also change other users' passwords.
#     """
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated]
    
#     def post(self, request, user_id=None):
#         """
#         Change password for the authenticated user or for a specific user (admin only)
        
#         If user_id is not provided, changes the authenticated user's password.
#         If user_id is provided, the authenticated user must be an admin to change someone else's password.
#         """
#         target_user = None
        
#         if user_id:
#             # Get the target user
#             target_user = get_object_or_404(User, id=user_id)
            
#             # Check permissions
#             if request.user.id != target_user.id and not (request.user.is_staff or request.user.is_superuser):
#                 return Response(
#                     {"error": "You don't have permission to change this user's password"},
#                     status=status.HTTP_403_FORBIDDEN
#                 )
#         else:
#             # No user_id provided, so target is the authenticated user
#             target_user = request.user
#             user_id = request.user.id
        
#         # Create serializer with context including request and user_id
#         serializer = PasswordChangeSerializer(
#             data=request.data,
#             context={'request': request, 'user_id': user_id}
#         )
        
#         if serializer.is_valid():
#             try:
#                 serializer.save()
                
#                 # Determine response message based on whether user is changing their own password
#                 if request.user.id == target_user.id:
#                     message = "Your password has been successfully changed"
#                 else:
#                     message = f"Password for user {target_user.username} has been successfully changed"
                
#                 return Response(
#                     {"message": message},
#                     status=status.HTTP_200_OK
#                 )
#             except Exception as e:
#                 return Response(
#                     {"error": f"Failed to change password: {str(e)}"},
#                     status=status.HTTP_500_INTERNAL_SERVER_ERROR
#                 )
#         else:
#             return Response(
#                 serializer.errors,
#                 status=status.HTTP_400_BAD_REQUEST
#             )

# class PasswordChangeView(LoggingMixin, APIView):
#     """
#     API endpoint that allows users to change their password.
#     If you're an admin, you can also change other users' passwords.
#     """
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated, IsOwnerOrAdmin]
    
#     def post(self, request, user_id=None):
#         """
#         Change password for the authenticated user or for a specific user (admin only)
        
#         If user_id is not provided, changes the authenticated user's password.
#         If user_id is provided, the authenticated user must be an admin to change someone else's password.
#         """
#         target_user = None
        
#         if user_id:
#             # Get the target user
#             target_user = get_object_or_404(User, id=user_id)
            
#             # Check permissions
#             # if request.user.id != target_user.id and not (request.user.is_staff or request.user.is_superuser):
#             if request.user.id != target_user.id and not (request.user.is_superuser):
#                 return Response(
#                     {"error": "You don't have permission to change this user's password"},
#                     status=status.HTTP_403_FORBIDDEN
#                 )
#         else:
#             # No user_id provided, so target is the authenticated user
#             target_user = request.user
#             user_id = request.user.id
            
#         # Check if admin is changing someone else's password
#         # is_admin_changing_other_password = (request.user.is_staff or request.user.is_superuser) and \
#         is_admin_changing_other_password = (request.user.is_superuser) and \
#                                           request.user.id != target_user.id
        
#         # Create serializer with context including request and user_id
#         serializer = PasswordChangeSerializer(
#             data=request.data,
#             context={'request': request, 'user_id': user_id}
#         )
        
#         if serializer.is_valid():
#             try:
#                 serializer.save()
                
#                 # Determine response message based on whether user is changing their own password
#                 if request.user.id == target_user.id:
#                     message = "Your password has been successfully changed"
#                 else:
#                     message = f"Password for user {target_user.username} has been successfully changed"
                
#                 return Response(
#                     {"message": message},
#                     status=status.HTTP_200_OK
#                 )
#             except Exception as e:
#                 return Response(
#                     {"error": f"Failed to change password: {str(e)}"},
#                     status=status.HTTP_500_INTERNAL_SERVER_ERROR
#                 )
#         else:
#             return Response(
#                 serializer.errors,
#                 status=status.HTTP_400_BAD_REQUEST
#             )

class PasswordChangeView(LoggingMixin, APIView):
    """
    API endpoint that allows users to change their password with metadata support.
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated, IsOwnerOrAdmin]
    
    def post(self, request, user_id=None):
        """
        Change password for the authenticated user or for a specific user (admin only)
        """
        # Initialize metadata
        metadata = {
            'success': False,
            'user_not_found': False,
            'permission_denied': False,
            'old_password_incorrect': False,
            'passwords_match': True,
            'password_min_length_error': False,
            'password_no_uppercase_error': False,
            'password_no_lowercase_error': False,
            'password_no_number_error': False,
            'password_no_special_char_error': False,
            'password_valid': True,
            'is_admin_changing_other_password': False
        }
        
        target_user = None
        
        if user_id:
            # Get the target user
            try:
                target_user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                metadata['user_not_found'] = True
                return Response(
                    {
                        "error": "User not found",
                        "metadata": metadata
                    },
                    status=status.HTTP_404_NOT_FOUND)
            
            # Check permissions
            if request.user.id != target_user.id and not request.user.is_superuser:
                metadata['permission_denied'] = True
                return Response(
                    {
                        "error": "You don't have permission to change this user's password",
                        "metadata": metadata
                    },
                    status=status.HTTP_403_FORBIDDEN
                )
        else:
            # No user_id provided, so target is the authenticated user
            target_user = request.user
            user_id = request.user.id
            
        # Check if admin is changing someone else's password
        is_admin_changing_other_password = (request.user.is_superuser) and \
                                          request.user.id != target_user.id
        
        metadata['is_admin_changing_other_password'] = is_admin_changing_other_password
        
        # Create serializer with context including request and user_id
        serializer = PasswordChangeSerializer(
            data=request.data,
            context={'request': request, 'user_id': user_id}
        )
        
        if serializer.is_valid():
            try:
                serializer.save()
                
                # Update metadata with validation results
                metadata.update(serializer.get_validation_metadata())
                metadata['success'] = True
                
                # Determine response message based on whether user is changing their own password
                if request.user.id == target_user.id:
                    message = "Your password has been successfully changed"
                else:
                    message = f"Password for user {target_user.username} has been successfully changed"
                
                return Response(
                    {
                        "message": message,
                        "metadata": metadata
                    },
                    status=status.HTTP_200_OK
                )
            except Exception as e:
                metadata['success'] = False
                return Response(
                    {
                        "error": f"Failed to change password: {str(e)}",
                        "metadata": metadata
                    },
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        else:
            # Update metadata based on validation errors
            metadata.update(serializer.get_validation_metadata() if hasattr(serializer, 'get_validation_metadata') else {})
            
            return Response(
                {
                    "errors": serializer.errors,
                    "metadata": metadata
                },
                status=status.HTTP_400_BAD_REQUEST
            )


class TestAuthenticationView(LoggingMixin, generics.GenericAPIView):
    permission_classes = []

    def get(self, request):
        data = {
            'msg': 'Access Token is working'
        }
        return Response(data, status=status.HTTP_200_OK)

class UserListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):

    serializer_class = UserSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = User.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['id']  # Fields that can be used for ordering
    ordering = ['id']  # Order by id ascending

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(created_by=user)
        return super().perform_create(serializer)
    
    # @swagger_auto_schema(
    #         operation_summary="List all users", 
    # )
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)
    
    # @swagger_auto_schema(
    #         operation_summary="Create a user", 
    # )
    def post(self, request: Request, *args, **kwargs):

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "User Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# class UserRetrieveUpdateDeleteView(
#     LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
# ):
    
#     serializer_class = UserSerializer
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated, IsOwnerOrAdmin]

#     queryset = User.objects.all()

#     def perform_update(self, serializer):
#         user = self.request.user
#         serializer.save(updated_by=user)
#         return super().perform_update(serializer)
    
#     # @swagger_auto_schema(
#     #         operation_summary="Retrieve a user by id", 
#     # )
#     def get(self, request: Request, *args, **kwargs):
#         return self.retrieve(request, *args, **kwargs)

#     # @swagger_auto_schema(
#     #         operation_summary="Update a user by id", 
#     # )
#     def put(self, request: Request, *args, **kwargs):

#         partial = kwargs.pop('partial', True)
#         instance = self.get_object()
#         serializer = self.get_serializer(instance, data=request.data, partial=partial)
#         if serializer.is_valid():
#             self.perform_update(serializer)
#             response = {"message": "User Updated Successfully", "data": serializer.data}
#             return Response(data=response, status=status.HTTP_200_OK)
#         return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

#     def delete(self, request: Request, *args, **kwargs):
#         try:
#             instance = self.get_object()
            
#             # Validate username confirmation
#             delete_serializer = UserDeleteSerializer(data=request.data)
#             if not delete_serializer.is_valid():
#                 return Response(
#                     delete_serializer.errors,
#                     status=status.HTTP_400_BAD_REQUEST
#                 )

#             # Check if provided username matches the user being deleted
#             if request.data.get('username') != instance.username:
#                 return Response(
#                     {"username": "Incorrect username provided"},
#                     status=status.HTTP_400_BAD_REQUEST
#                 )

#             # Perform the deletion
#             self.perform_destroy(instance)
            
#             return Response(
#                 {"message": "User Deleted Successfully"},
#                 status=status.HTTP_200_OK
#             )

#         except Exception as e:
#             print(f"Error deleting user: {str(e)}")
#             return Response(
#                 {"error": "Failed to delete user", "detail": str(e)},
#                 status=status.HTTP_500_INTERNAL_SERVER_ERROR
#             )

class UserRetrieveUpdateDeleteView(
    LoggingMixin, 
    generics.GenericAPIView, 
    mixins.RetrieveModelMixin, 
    mixins.UpdateModelMixin, 
    mixins.DestroyModelMixin
):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated, IsOwnerOrAdmin]
    queryset = User.objects.all()
    
    def get_serializer_class(self):
        """Return appropriate serializer based on request method"""
        if self.request.method == 'PUT':
            return UserUpdateSerializer
        return UserResponseSerializer
    
    def perform_update(self, serializer):
        """Add updated_by field during update"""
        serializer.save(updated_by=self.request.user)
    
    def get(self, request: Request, *args, **kwargs):
        """Retrieve a user by id with standardized response"""
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        
        response_data = {
            "message": "User retrieved successfully",
            "data": serializer.data
        }
        
        return Response(data=response_data, status=status.HTTP_200_OK)
    
    def put(self, request: Request, *args, **kwargs):
        """Update a user by id with standardized response"""
        partial = kwargs.pop('partial', True)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        
        # Initialize metadata
        metadata = {
            'success': False,
            'duplicated_work_email': False,
            'passwords_match': True,
            'password_min_length_error': False,
            'password_no_uppercase_error': False,
            'password_no_lowercase_error': False,
            'password_no_number_error': False,
            'password_no_special_char_error': False,
            'password_valid': True,
            'password_changed': False
        }
        
        if serializer.is_valid():
            # Update metadata with validation results
            metadata.update(serializer.get_validation_metadata() if hasattr(serializer, 'get_validation_metadata') else {})
            metadata['success'] = True
            
            # Perform the update
            self.perform_update(serializer)
            
            # Use response serializer for consistent output
            response_serializer = UserResponseSerializer(instance)
            
            response_data = {
                "message": "User updated successfully",
                "data": response_serializer.data,
                "metadata": metadata
            }
            
            return Response(data=response_data, status=status.HTTP_200_OK)
        else:
            # Update metadata based on validation errors
            metadata.update(serializer.get_validation_metadata() if hasattr(serializer, 'get_validation_metadata') else {})
            
            response_data = {
                "message": "Validation failed",
                "errors": serializer.errors,
                "metadata": metadata
            }
            
            return Response(data=response_data, status=status.HTTP_400_BAD_REQUEST)
    
    def delete(self, request: Request, *args, **kwargs):
        """Delete a user with username confirmation"""
        try:
            instance = self.get_object()
            
            # Validate username confirmation
            delete_serializer = UserDeleteSerializer(data=request.data)
            if not delete_serializer.is_valid():
                return Response(
                    {
                        "message": "Validation failed",
                        "errors": delete_serializer.errors
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Check if provided username matches the user being deleted
            if request.data.get('username') != instance.username:
                return Response(
                    {
                        "message": "Validation failed",
                        "errors": {"username": ["Incorrect username provided"]}
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Perform the deletion (soft delete)
            self.perform_destroy(instance)
            
            return Response(
                {"message": "User deleted successfully"},
                status=status.HTTP_200_OK
            )
            
        except Exception as e:
            self.log_error(f"Error deleting user: {str(e)}")
            return Response(
                {
                    "message": "Failed to delete user",
                    "errors": {"detail": str(e)}
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

# ========== PAGINATION CLASSES ==========
class StandardResultsPagination(PageNumberPagination):
    page_size = 10
    page_size_query_param = 'page_size'  # Change from 'limit' to 'page_size' for clarity
    max_page_size = 100


# # ========== FILTER CLASSES ==========
# class UserFilter(django_filters.FilterSet):
#     """
#     Custom filter for User model supporting multiple values per filter
#     """
#     status = django_filters.CharFilter(method='filter_status')
#     role = django_filters.CharFilter(method='filter_role')
#     partner = django_filters.CharFilter(method='filter_partner')
    
#     class Meta:
#         model = User
#         fields = ['id', 'status', 'role', 'partner', 'is_active']
    
#     def filter_status(self, queryset, name, value):
#         """
#         Filter users by status (supports multiple comma-separated values)
#         Format: 'status=online,away,busy'
#         """
#         if not value:
#             return queryset
            
#         statuses = [s.strip().lower() for s in value.split(',')]
#         valid_statuses = [choice[0].lower() for choice in User.StatusChoices.choices]
#         statuses = [s for s in statuses if s in valid_statuses]
        
#         if not statuses:
#             return queryset
            
#         return queryset.filter(status__in=statuses)
    
#     def filter_role(self, queryset, name, value):
#         """
#         Filter users by role names (supports multiple comma-separated values)
#         Format: 'role=admin,supervisor,agent'
#         """
#         if not value:
#             return queryset
            
#         role_names = [r.strip() for r in value.split(',')]
#         if not role_names:
#             return queryset
            
#         # Get users who have any of the specified roles
#         return queryset.filter(
#             userrole__role_id__name__in=role_names,
#             userrole__role_id__is_active=True
#         ).distinct()
    
#     def filter_partner(self, queryset, name, value):
#         """
#         Filter users by partner/company (supports multiple comma-separated values)
#         Format: 'partner=1,2,3' or 'partner=company1,company2'
#         """
#         if not value:
#             return queryset
            
#         partners = value.split(',')
#         if not partners:
#             return queryset
            
#         # Try to filter by partner IDs first
#         try:
#             partner_ids = [int(p) for p in partners]
#             return queryset.filter(partner_id__in=partner_ids)
#         except ValueError:
#             # If not IDs, try filtering by partner names
#             return queryset.filter(partner__name__in=partners)

# # ========== USER VIEWS ==========
# class UserListPaginatedView(generics.ListAPIView):
#     """
#     Paginated list of users with filters for status, role, and partner.
    
#     Endpoint: GET /api/users/paginated/
    
#     Query Parameters:
#     - status: Filter by status (comma-separated) e.g., ?status=online,away
#     - role: Filter by role names (comma-separated) e.g., ?role=admin,agent
#     - partner: Filter by partner ID or name (comma-separated) e.g., ?partner=1,2
#     - search: Search in username, email, first_name, last_name
#     - ordering: Order by fields e.g., ?ordering=-created_on,username
#     - limit: Number of results per page (default: 20, max: 100)
#     - page: Page number
#     """
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated]
    
#     pagination_class = StandardResultsPagination
    
#     filter_backends = [
#         django_filters.DjangoFilterBackend,
#         filters.SearchFilter,
#         filters.OrderingFilter
#     ]
#     filterset_class = UserFilter
#     search_fields = ['username', 'email', 'first_name', 'last_name', 'employee_id']
#     ordering_fields = ['id', 'created_on', 'updated_on', 'last_login']
#     ordering = ['-created_on']  # Default ordering
    
#     def get_queryset(self):
#         """
#         Optimized queryset with select_related and prefetch_related
#         """
#         queryset = User.objects.select_related(
#             'department',
#             'partner'
#         ).prefetch_related(
#             Prefetch(
#                 'userrole_set',
#                 queryset=UserRole.objects.select_related('role_id').filter(
#                     role_id__is_active=True
#                 ),
#                 to_attr='active_roles'
#             ),
#             'user_tags'
#         )
        
#         # Only show active users by default
#         queryset = queryset.filter(is_active=True)
        
#         return queryset
    
#     def list(self, request, *args, **kwargs):
#         """
#         Override list to add custom response format
#         """
#         queryset = self.filter_queryset(self.get_queryset())
#         page = self.paginate_queryset(queryset)
        
#         if page is not None:
#             users = []
#             for user in page:
#                 # Get user's roles
#                 roles = []
#                 for user_role in user.active_roles:
#                     roles.append({
#                         'id': user_role.role_id.role_id,
#                         'name': user_role.role_id.name
#                     })
                
#                 # Get user's tags
#                 tags = list(user.user_tags.values('id', 'name', 'color'))
                
#                 users.append({
#                     'id': user.id,
#                     'username': user.username,
#                     'email': user.email,
#                     'employee_id': user.employee_id,
#                     'name': user.get_full_name() or user.name,
#                     'first_name': user.first_name,
#                     'last_name': user.last_name,
#                     'status': {
#                         'value': user.status,
#                         'display': user.get_status_display() if hasattr(user, 'get_status_display') else user.status
#                     },
#                     'roles': roles,
#                     'department': {
#                         'id': user.department.id if user.department else None,
#                         'name': user.department.name if user.department else None
#                     } if user.department else None,
#                     'partner': {
#                         'id': user.partner.id if user.partner else None,
#                         'name': user.partner.name if user.partner else None
#                     } if user.partner else None,
#                     'tags': tags,
#                     'is_online': user.status == 'online',
#                     'last_login': user.last_login.isoformat() if user.last_login else None,
#                     'created_on': user.created_on.isoformat() if user.created_on else None
#                 })
            
#             return self.get_paginated_response(users)
        
#         return Response(users)

# ========== FILTER CLASSES ==========
class UserFilter(django_filters.FilterSet):
    """
    Custom filter for User model supporting multiple values per filter
    """
    status = django_filters.CharFilter(method='filter_status')
    role = django_filters.CharFilter(method='filter_role')
    partner = django_filters.CharFilter(method='filter_partner')
    department = django_filters.CharFilter(method='filter_department')
    user_tag = django_filters.CharFilter(method='filter_user_tag')
    is_active = django_filters.BooleanFilter(method='filter_active_status')
    
    class Meta:
        model = User
        fields = ['id', 'status', 'role', 'partner', 'department', 'user_tag', 'is_active']
    
    def filter_status(self, queryset, name, value):
        """
        Filter users by status (supports multiple comma-separated values)
        Format: 'status=online,away,busy'
        """
        if not value:
            return queryset
            
        statuses = [s.strip().lower() for s in value.split(',')]
        valid_statuses = [choice[0].lower() for choice in User.StatusChoices.choices]
        statuses = [s for s in statuses if s in valid_statuses]
        
        if not statuses:
            return queryset
            
        return queryset.filter(status__in=statuses)
    
    def filter_role(self, queryset, name, value):
        """
        Filter users by role names (supports multiple comma-separated values)
        Format: 'role=admin,supervisor,agent'
        """
        if not value:
            return queryset
            
        role_names = [r.strip() for r in value.split(',')]
        if not role_names:
            return queryset
            
        # Get users who have any of the specified roles
        return queryset.filter(
            userrole__role_id__name__in=role_names,
            userrole__role_id__is_active=True
        ).distinct()
    
    def filter_partner(self, queryset, name, value):
        """
        Filter users by partner/company (supports multiple comma-separated values)
        Format: 'partner=1,2,3' or 'partner=company1,company2'
        """
        if not value:
            return queryset
            
        partners = value.split(',')
        if not partners:
            return queryset
            
        # Try to filter by partner IDs first
        try:
            partner_ids = [int(p) for p in partners]
            return queryset.filter(partners__id__in=partner_ids).distinct()
        except ValueError:
            # If not IDs, try filtering by partner names
            return queryset.filter(partners__name__in=partners).distinct()
    
    def filter_department(self, queryset, name, value):
        """
        Filter users by department (supports multiple comma-separated values)
        Format: 'department=1,2,3' or 'department=CS,TS'
        """
        if not value:
            return queryset
            
        departments = value.split(',')
        if not departments:
            return queryset
            
        # Try to filter by department IDs first
        try:
            dept_ids = [int(d) for d in departments]
            return queryset.filter(departments__id__in=dept_ids).distinct()
        except ValueError:
            # If not IDs, try filtering by department names or codes
            return queryset.filter(
                Q(departments__name__in=departments) | 
                Q(departments__code__in=departments)
            ).distinct()
    
    def filter_user_tag(self, queryset, name, value):
        """
        Filter users by user tags (supports multiple comma-separated values)
        Format: 'user_tag=1,2,3' or 'user_tag=tag1,tag2'
        """
        if not value:
            return queryset
            
        tags = value.split(',')
        if not tags:
            return queryset
            
        # Try to filter by tag IDs first
        try:
            tag_ids = [int(t) for t in tags]
            return queryset.filter(user_tags__id__in=tag_ids).distinct()
        except ValueError:
            # If not IDs, try filtering by tag names
            return queryset.filter(user_tags__name__in=tags).distinct()
    
    def filter_active_status(self, queryset, name, value):
        """
        Filter users based on active status
        """
        if value is None:
            return queryset
        return queryset.filter(is_active=value)

class UserListPaginatedView(generics.ListAPIView):
    """
    Paginated list of users with filters for status, role, and partner.
    
    Endpoint: GET /api/users/paginated/
    
    Query Parameters:
    - status: Filter by status (comma-separated) e.g., ?status=online,away
    - role: Filter by role names (comma-separated) e.g., ?role=admin,agent
    - partner: Filter by partner ID or name (comma-separated) e.g., ?partner=1,2
    - department: Filter by department ID or name (comma-separated) e.g., ?department=1,2
    - user_tag: Filter by user tag ID or name (comma-separated) e.g., ?user_tag=1,2
    - search: Search across multiple fields (split by space): no./ID, status, name, role, partner, department, tag
    - ordering: Order by fields e.g., ?ordering=-created_on,username
    - page_size: Number of results per page (default: 10, max: 100)
    - page: Page number
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    pagination_class = StandardResultsPagination
    
    filter_backends = [
        django_filters.DjangoFilterBackend,
        filters.OrderingFilter  # Remove SearchFilter as we'll implement custom search
    ]
    filterset_class = UserFilter
    ordering_fields = ['id', 'status', 'current_workload', 'first_name', 'last_active']  # Only allow sorting by clickable table columns
    ordering = ['id']  # Default ordering
    
    def get_queryset(self):
        """
        Optimized queryset with select_related and prefetch_related
        """
        queryset = User.objects.select_related(
            'work_schedule' # Fetch UserSchedule in one query
        ).prefetch_related(
            'departments',  # ManyToMany field
            'partners',     # ManyToMany field
            Prefetch(
                'userrole_set',
                queryset=UserRole.objects.select_related('role_id').filter(
                    role_id__is_active=True
                ),
                to_attr='active_roles'
            ),
            'user_tags'
        )
        
        # Return all users regardless of is_active status

        # Only show active users by default
        queryset = queryset.filter(is_active=True)
        
        return queryset
    
    def filter_queryset(self, queryset):
        """
        Override filter_queryset to add custom search and secondary sorting by id
        """
        # Apply DjangoFilterBackend filters first (status, role, partner, etc.)
        for backend in self.filter_backends:
            if backend == filters.OrderingFilter:
                continue  # Skip ordering for now, we'll handle it later
            queryset = backend().filter_queryset(self.request, queryset, self)
        
        # Apply custom search
        search_query = self.request.query_params.get('search', '').strip()
        if search_query:
            queryset = self.apply_custom_search(queryset, search_query)
        
        # Debug: Log queryset count after filtering
        print(f"DEBUG: Queryset count after filtering: {queryset.count()}")
        
        # Get ordering from query params
        ordering = self.request.query_params.get('ordering', '')
        
        # Default ordering
        if not ordering:
            return queryset.order_by('id')
        
        # Check if this is a time field that should be reversed for intuitive sorting
        time_fields = ['last_active', 'created_on', 'last_login']
        ordering_field = ordering.lstrip('-')  # Remove leading minus sign to get base field
        
        if ordering_field in time_fields:
            # For time fields, reverse the sorting direction for more intuitive behavior
            # If frontend sends 'last_active' (ascending), we want descending (newer first)
            # If frontend sends '-last_active' (descending), we want ascending (older first)
            if ordering.startswith('-'):
                # Frontend wants descending, give them ascending (older first)
                final_queryset = queryset.order_by(ordering_field, 'id')
            else:
                # Frontend wants ascending, give them descending (newer first)
                final_queryset = queryset.order_by(f'-{ordering_field}', 'id')
        else:
            # For other fields, add 'id' as secondary sort
            final_queryset = queryset.order_by(ordering, 'id')
        
        # Debug: Log final ordering
        print(f"DEBUG: Final ordering: {final_queryset.query.order_by}")
        
        return final_queryset
    
    def apply_custom_search(self, queryset, search_query):
        """
        Apply custom search logic that splits by spaces and searches across multiple fields:
        - no./ID (id, employee_id, username)
        - status
        - name (first_name, last_name, full name)
        - role (any role the user has)
        - partner (any partner the user belongs to)
        - department (any department the user belongs to)
        - tag (any tag the user has)
        """
        # Split search query by spaces
        search_terms = [term.strip() for term in search_query.split() if term.strip()]
        
        if not search_terms:
            return queryset
        
        # For each search term, create a Q object that matches any of the fields
        from django.db.models import Q
        
        final_q = Q()
        
        for term in search_terms:
            term_q = Q()
            
            # 1. No./ID fields (id, employee_id, username)
            try:
                # Try to match as integer ID
                if term.isdigit():
                    term_q |= Q(id=int(term))
            except ValueError:
                pass
            
            # Search in employee_id and username (case-insensitive)
            term_q |= Q(employee_id__icontains=term)
            term_q |= Q(username__icontains=term)
            
            # 2. Status field (case-insensitive)
            term_q |= Q(status__icontains=term)
            
            # 3. Name fields (first_name, last_name, email)
            term_q |= Q(first_name__icontains=term)
            term_q |= Q(last_name__icontains=term)
            term_q |= Q(email__icontains=term)
            
            # 4. Role - any role the user has
            term_q |= Q(
                userrole__role_id__name__icontains=term,
                userrole__role_id__is_active=True
            )
            
            # 5. Partner - any partner/company the user belongs to
            term_q |= Q(partners__name__icontains=term)
            term_q |= Q(partners__code__icontains=term)
            
            # 6. Department - any department the user belongs to
            term_q |= Q(departments__name__icontains=term)
            term_q |= Q(departments__code__icontains=term)
            
            # 7. Tag - any tag the user has
            term_q |= Q(user_tags__name__icontains=term)
            
            # Add this term's Q object to the final query
            final_q &= term_q
        
        # Apply the search and remove duplicates
        return queryset.filter(final_q).distinct()
    
    def list(self, request, *args, **kwargs):
        """
        Override list to add custom response format
        """
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        
        if page is not None:

            # Cache company business hours to avoid repeated DB queries
            company_business_hours = None
            try:
                # TODO - Use Service to get cache values of System Setting instead
                from setting.models import SystemSettings
                business_hours_setting = SystemSettings.objects.filter(
                    key='COMPANY_BUSINESS_HOURS',
                    # is_active=True
                ).first()
                company_business_hours = json.loads(business_hours_setting.value) if business_hours_setting else {}

            except Exception as e:
                company_business_hours = {}
                print(f"company_business_hours (EXCEPT)'S ERROR- {e}")

            users = []
            for user in page:
                # Get user's roles
                roles = []
                for user_role in user.active_roles:
                    roles.append({
                        'id': user_role.role_id.role_id,
                        'name': user_role.role_id.name
                    })
                
                # Get user's tags - Return full objects like departments and partners
                tags = []
                for tag in user.user_tags.all():
                    tags.append({
                        'id': tag.id,
                        'name': tag.name,
                        'color': tag.color
                    })
                
                # Get user's departments (ManyToMany)
                departments = []
                for dept in user.departments.all():
                    departments.append({
                        'id': dept.id,
                        'name': dept.name,
                        'code': dept.code,
                        'color': dept.color
                    })
                
                # Get user's partners (ManyToMany)
                partners = []
                for partner in user.partners.all():  
                    partners.append({
                        'id': partner.id,
                        'name': partner.name,
                        'code': partner.code,
                        'color': getattr(partner, 'color', 'gray')  # Company model might not have color
                    })

                # Get user's work schedule
                work_schedule = None
                if hasattr(user, 'work_schedule'):
                    # Use cached company business hours when needed
                    if user.work_schedule.same_as_business_hours:
                        # Use cached company business hours
                        schedule_data = company_business_hours
                    else:
                        # Use user's custom schedule
                        schedule_data = user.work_schedule.schedule
                    
                    work_schedule = {
                        'same_as_business_hours': user.work_schedule.same_as_business_hours,
                        'schedule': schedule_data,
                        'created_on': user.work_schedule.created_on.isoformat() if user.work_schedule.created_on else None,
                        'updated_on': user.work_schedule.updated_on.isoformat() if user.work_schedule.updated_on else None
                    }
                
                users.append({
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'employee_id': user.employee_id,
                    'name': user.get_full_name() or user.name,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'status': user.status,  # Return status as string to match frontend expectations
                    'current_workload': getattr(user, 'current_workload', 0),
                    'last_active': user.last_active.isoformat() if hasattr(user, 'last_active') and user.last_active else None,
                    'roles': ', '.join([role['name'] for role in roles]),  # Return as comma-separated string
                    'departments': departments,
                    'partners': partners,
                    'user_tags': tags,  # Return full tag objects like departments and partners
                    'work_schedule': work_schedule,
                    'is_active': user.is_active,
                    'is_online': user.status == 'online',
                    'last_login': user.last_login.isoformat() if user.last_login else None,
                    'created_on': user.created_on.isoformat() if user.created_on else None
                })
            
            return self.get_paginated_response(users)
        
        return Response(users)

class RoleListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):

    serializer_class = RoleSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = Role.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['role_id']  # Fields that can be used for ordering
    ordering = ['role_id']  # Order by id ascending

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(created_by=user)
        return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request: Request, *args, **kwargs):

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "Role Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class RoleRetrieveUpdateDeleteView(
    LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
):
    
    serializer_class = RoleSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = Role.objects.all()

    def perform_update(self, serializer):
        user = self.request.user
        serializer.save(updated_by=user)
        return super().perform_update(serializer)

    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):

        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            response = {"message": "Role Updated Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_200_OK)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request: Request, *args, **kwargs):

        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "Role Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)
    
class PermissionListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):

    serializer_class = PermissionSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = Permission.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['id']  # Fields that can be used for ordering
    ordering = ['id']  # Order by id ascending

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(created_by=user)
        return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request: Request, *args, **kwargs):

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "Permission Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class PermissionRetrieveUpdateDeleteView(
    LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
):
    
    serializer_class = PermissionSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = Permission.objects.all()

    def perform_update(self, serializer):
        user = self.request.user
        serializer.save(updated_by=user)
        return super().perform_update(serializer)

    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):

        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            response = {"message": "Permission Updated Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_200_OK)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request: Request, *args, **kwargs):

        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "Permission Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)

class UserRoleListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):

    serializer_class = UserRoleSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = UserRole.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['id']  # Fields that can be used for ordering
    ordering = ['id']  # Order by id ascending

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(created_by=user)
        return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request: Request, *args, **kwargs):

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "UserRole Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class UserRoleRetrieveUpdateDeleteView(
    LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
):
    
    serializer_class = UserRoleSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = UserRole.objects.all()

    def perform_update(self, serializer):
        user = self.request.user
        serializer.save(updated_by=user)
        return super().perform_update(serializer)

    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):

        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            response = {"message": "UserRole Updated Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_200_OK)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request: Request, *args, **kwargs):

        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "UserRole Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)
    
class RolePermissionListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):

    serializer_class = RolePermissionSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = RolePermission.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['id']  # Fields that can be used for ordering
    ordering = ['id']  # Order by id ascending

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(created_by=user)
        return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request: Request, *args, **kwargs):

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "RolePermission Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class RolePermissionRetrieveUpdateDeleteView(
    LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
):
    
    serializer_class = RolePermissionSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = RolePermission.objects.all()

    def perform_update(self, serializer):
        user = self.request.user
        serializer.save(updated_by=user)
        return super().perform_update(serializer)

    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):

        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            response = {"message": "RolePermission Updated Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_200_OK)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request: Request, *args, **kwargs):

        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "RolePermission Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)

class UserStatusListView(generics.ListAPIView):
    """View for listing all users with their statuses"""
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        if self.request.user.is_staff:
            return User.objects.all()
        return User.objects.filter(
            partners__in=self.request.user.partners.all()
        ).distinct()

class UserStatusUpdateView(generics.UpdateAPIView):
    """View for updating user status"""
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]
    
    def patch(self, request, *args, **kwargs):
        try:
            new_status = request.data.get('new_status')
            if new_status not in User.StatusChoices.values:
                return Response(
                    {'error': 'Invalid status'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            success = UserStatusService.update_user_status(
                request.user.id,
                new_status,
                is_auto=False
            )

            if success:
                # Refresh the user instance from the database to get updated values
                user = User.objects.get(id=request.user.id)
                serializer = self.get_serializer(user)
                return Response(serializer.data)
            
            return Response(
                {'error': 'Failed to update status'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

# class MyStatusView(generics.RetrieveAPIView):
#     """View for getting current user's status"""
#     serializer_class = UserSerializer
#     permission_classes = [IsAuthenticated]

#     def get_object(self):
#         return self.request.user

class AvailableUsersView(generics.ListAPIView):
    """View for getting available users for a company"""
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        company_code = self.request.query_params.get('company_code')
        if not company_code:
            return User.objects.none()

        available_user = UserStatusService.get_suitable_user(company_code)
        if available_user:
            return User.objects.filter(id=available_user.id)
        return User.objects.none()
    
class AssignRolesView(generics.GenericAPIView):
    serializer_class = RoleAssignmentSerializer
    permission_classes = [IsAuthenticated]

    def post(self, request, user_id):
        user = get_object_or_404(User, id=user_id)
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            roles = Role.objects.filter(role_id__in=serializer.validated_data['role_ids'])
            
            # Create UserRole entries
            for role in roles:
                UserRole.objects.get_or_create(
                    user_id=user,
                    role_id=role,
                    defaults={'created_by': request.user}
                )
            
            # Return updated list of user's roles
            user_roles = UserRole.objects.filter(user_id=user)
            response_serializer = UserRoleSerializer(user_roles, many=True)
            
            return Response({
                'message': 'Roles assigned successfully',
                'roles': response_serializer.data
            }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class RemoveRolesView(generics.GenericAPIView):
    serializer_class = RoleAssignmentSerializer
    permission_classes = [IsAuthenticated]

    def post(self, request, user_id):
        user = get_object_or_404(User, id=user_id)
        serializer = self.get_serializer(data=request.data)
        
        if serializer.is_valid():
            # Delete UserRole entries
            UserRole.objects.filter(
                user_id=user,
                role_id__role_id__in=serializer.validated_data['role_ids']
            ).delete()
            
            # Return remaining roles
            user_roles = UserRole.objects.filter(user_id=user)
            response_serializer = UserRoleSerializer(user_roles, many=True)
            
            return Response({
                'message': 'Roles removed successfully',
                'roles': response_serializer.data
            }, status=status.HTTP_200_OK)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class ListUserRolesView(generics.ListAPIView):
    serializer_class = UserRoleSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user_id = self.kwargs['user_id']
        user = get_object_or_404(User, id=user_id)
        return UserRole.objects.filter(user_id=user)

class UsersWithInfosView(generics.ListAPIView):
    serializer_class = UsersInfosSerializer
    permission_classes = [IsAuthenticated]
    
    filter_backends = [OrderingFilter]
    ordering_fields = ['id']  # Fields that can be used for ordering
    ordering = ['id']  # Order by user's id ascending

    def get_queryset(self):
        return User.objects.all().prefetch_related(
            'userrole_set',
            'userrole_set__role_id',
            'partners',
            'departments',
            'work_schedule'
        )
    
class UserWithInfoView(generics.RetrieveAPIView):
    serializer_class = UsersInfosSerializer
    permission_classes = [IsAuthenticated]
    
    queryset = User.objects.all()
    lookup_field = 'id'
    
    filter_backends = [OrderingFilter]
    ordering_fields = ['id']  # Fields that can be used for ordering
    ordering = ['id']  # Order by user's id ascending
    
    def get_queryset(self):
        user_id = self.kwargs.get('id')
        check_user_workload(user_id=user_id)
        return User.objects.all().prefetch_related(
            'userrole_set',
            'userrole_set__role_id',
            'partners',
            'departments',
            'work_schedule'
        )

class UserFileUploadView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def post(self, request, employee_id):
        """
        Upload a file for a specific user
        URL: POST /user/{employee_id}/files/upload/
        """
        if 'file' not in request.FILES:
            return Response({
                'error': 'No file was submitted. Please include a file with the key "file"'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = get_object_or_404(User, employee_id=employee_id)
            file = request.FILES['file']
            
            filename = file.name.lower()
            allowed_extensions = ['.jpg', '.jpeg', '.png', '.pdf']
            allowed_types = ['image/jpeg', 'image/png', 'application/pdf']
            
            is_valid_extension = any(filename.endswith(ext) for ext in allowed_extensions)
            is_valid_content_type = file.content_type in allowed_types
            
            if not (is_valid_extension and is_valid_content_type):
                return Response({
                    'error': 'Invalid file type. Allowed types are: JPEG, PNG, PDF',
                    'received_content_type': file.content_type,
                    'filename': filename
                }, status=status.HTTP_400_BAD_REQUEST)
            
            file_url = user.upload_file(file)
            
            return Response({
                'message': 'File uploaded successfully',
                'url': file_url,
                'filename': file.name,
                'size': file.size
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                'error': f'Error uploading file: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

class UserFileDeleteView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def delete(self, request, employee_id, filename):
        """
        Delete a specific file for a user
        URL: DELETE /user/{employee_id}/files/delete/{filename}/
        """
        try:
            user = get_object_or_404(User, employee_id=employee_id)
            
            files = user.list_files()
            file_exists = any(file['name'] == filename for file in files)
            
            if not file_exists:
                return Response({
                    'error': f'File {filename} not found for user {employee_id}'
                }, status=status.HTTP_404_NOT_FOUND)
            
            success = user.delete_file(filename)
            
            if success:
                return Response({
                    'message': f'File {filename} deleted successfully'
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'error': f'Failed to delete file {filename}'
                }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'error': f'Error deleting file: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

class UserFileListView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def get(self, request, employee_id):
        """
        List all files for a specific user
        URL: GET /user/{employee_id}/files/
        """
        try:
            user = get_object_or_404(User, employee_id=employee_id)
            files = user.list_files()
            
            return Response({
                'files': files
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

class UserFileDownloadView(APIView):
    authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]
    permission_classes = []

    def get(self, request, employee_id, filename):
        """
        Download a specific file for a user
        URL: GET /user/{employee_id}/files/download/{filename}/
        """
        try:
            user = get_object_or_404(User, employee_id=employee_id)
            
            files = user.list_files()
            file_info = next((file for file in files if file['name'] == filename), None)
            
            if not file_info:
                return Response({
                    'error': f'File {filename} not found'
                }, status=status.HTTP_404_NOT_FOUND)

            azure_storage = AzureBlobStorage()
            blob_name = f"{user.blob_folder}{filename}"
            
            try:
                blob_client = azure_storage.container_client.get_blob_client(blob_name)
                download_stream = blob_client.download_blob()
                content_type = download_stream.properties.content_settings.content_type or 'application/octet-stream'
                
                response = HttpResponse(
                    download_stream.readall(),
                    content_type=content_type
                )
                response['Content-Disposition'] = f'attachment; filename="{filename}"'
                return response
                
            except Exception as azure_error:
                return Response({
                    'error': f'Error accessing file from storage: {str(azure_error)}'
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'error': f'Error downloading file: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

# TODO - Keep this first, when do the frontend, check whether the POST or GET method can accept JSON body
# class UserBulkFileDownloadView(APIView):
#     authentication_classes = [JWTAuthentication]
#     permission_classes = [IsAuthenticated]

#     def get(self, request, employee_id):
#         """
#         Download multiple files as ZIP for a user
#         URL: GET /user/{employee_id}/files/download-bulk/
#         Query params: filenames=file1.jpg,file2.pdf (optional)
#         If filenames is not provided, downloads all files
#         """
#         try:
#             user = get_object_or_404(User, employee_id=employee_id)
#             azure_storage = AzureBlobStorage()
            
#             available_files = user.list_files()
            
#             # Get filenames from query parameters
#             filenames_param = request.query_params.get('filenames', '')
#             requested_filenames = [f.strip() for f in filenames_param.split(',')] if filenames_param else []
            
#             files_to_download = available_files
#             if requested_filenames:
#                 files_to_download = [
#                     file for file in available_files 
#                     if file['name'] in requested_filenames
#                 ]
            
#             if not files_to_download:
#                 return Response({
#                     'error': 'No files found to download'
#                 }, status=status.HTTP_404_NOT_FOUND)
            
#             zip_buffer = io.BytesIO()
#             with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
#                 for file_info in files_to_download:
#                     try:
#                         filename = file_info['name']
#                         blob_name = f"{user.blob_folder}{filename}"
                        
#                         blob_client = azure_storage.container_client.get_blob_client(blob_name)
#                         download_stream = blob_client.download_blob()
                        
#                         zip_file.writestr(filename, download_stream.readall())
                        
#                     except Exception as e:
#                         continue
            
#             zip_buffer.seek(0)
#             response = HttpResponse(
#                 zip_buffer.getvalue(),
#                 content_type='application/zip'
#             )
#             timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
#             response['Content-Disposition'] = f'attachment; filename="user_{employee_id}_files_{timestamp}.zip"'
            
#             return response

#         except Exception as e:
#             return Response({
#                 'error': f'Error downloading files: {str(e)}'
#             }, status=status.HTTP_400_BAD_REQUEST)
        
class UserBulkFileDownloadView(APIView):
    authentication_classes = [JWTAuthentication]
    # permission_classes = [IsAuthenticated]
    permission_classes = []

    def post(self, request, employee_id):
        """
        Download multiple files as ZIP for a user
        URL: POST /user/{employee_id}/files/download-bulk/
        Request body: {"filenames": ["file1.jpg", "file2.pdf"]} (optional)
        If filenames is not provided or empty list, downloads all files
        """
        try:
            user = get_object_or_404(User, employee_id=employee_id)
            azure_storage = AzureBlobStorage()
            
            available_files = user.list_files()
            print(f"Available files for user {employee_id}: {available_files}")
            
            # Get list of files to download from request body
            requested_filenames = request.data.get('filenames', [])
            print(f"Requested filenames: {requested_filenames}")
            
            # If no filenames provided or empty list, download all files
            files_to_download = available_files
            if requested_filenames:
                files_to_download = [
                    file for file in available_files 
                    if file['name'] in requested_filenames
                ]
            print(f"Files to download: {files_to_download}")
            
            if not files_to_download:
                return Response({
                    'error': 'No files found to download'
                }, status=status.HTTP_404_NOT_FOUND)
            
            zip_buffer = io.BytesIO()
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                for file_info in files_to_download:
                    try:
                        filename = file_info['name']
                        blob_name = f"{user.blob_folder}{filename}"
                        
                        blob_client = azure_storage.container_client.get_blob_client(blob_name)
                        download_stream = blob_client.download_blob()
                        
                        zip_file.writestr(filename, download_stream.readall())
                        
                    except Exception as e:
                        continue
            
            zip_buffer.seek(0)
            response = HttpResponse(
                zip_buffer.getvalue(),
                content_type='application/zip'
            )
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            response['Content-Disposition'] = f'attachment; filename="user_{employee_id}_files_{timestamp}.zip"'
            
            return response

        except Exception as e:
            return Response({
                'error': f'Error downloading files: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
class GetUserTicketsView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request, employee_id):
        """
        Get all tickets associated with a specific user
        
        Args:
            employee_id: Employee ID of the user to fetch tickets for
            
        Returns:
            List of ticket objects associated with the user
        """
        try:
            # Verify user exists
            user = get_object_or_404(User, employee_id=employee_id)
            
            # # Check if requesting user has permission to view these tickets
            # if request.user != user and not request.user.is_superuser:
            #     return Response({
            #         'error': 'You do not have permission to view these tickets'
            #     }, status=status.HTTP_403_FORBIDDEN)
            
            # Get all tickets where the user is the owner
            tickets = Ticket.objects.filter(owner_id=user)
            
            # Serialize the tickets
            serializer = TicketSerializer(tickets, many=True)
            
            return Response({
                'employee_id': user.employee_id,
                'name': user.name,
                'username': user.username,
                'current_workload': user.current_workload,
                'status': user.status,
                'tickets': serializer.data,
                'total_tickets': tickets.count()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error fetching tickets: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
class UserTicketsView(APIView):
    """
    View for retrieving all tickets owned by a specific user.
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, user_id=None):
        """
        Get all tickets owned by a specific user
        
        If user_id is not provided, returns tickets owned by the requesting user.
        If user_id is provided, only admins or the user themselves can see their tickets.
        """
        # Determine target user
        if user_id is None:
            # No user_id provided, use the requesting user
            target_user = request.user
        else:
            # User ID was provided
            try:
                target_user = User.objects.get(id=user_id)
                
                # # Check if requesting user has permission to see this user's tickets
                # if request.user.id != target_user.id and not request.user.is_staff and not request.user.is_superuser:
                #     return Response({
                #         'error': 'You do not have permission to view this user\'s tickets'
                #     }, status=status.HTTP_403_FORBIDDEN)
                    
            except User.DoesNotExist:
                return Response({
                    'error': f'User with ID {user_id} not found'
                }, status=status.HTTP_404_NOT_FOUND)
        
        # Get all tickets owned by the target user
        tickets = Ticket.objects.filter(owner_id=target_user)
        
        # Optional filtering by status
        status_id = request.query_params.get('status_id')
        if status_id:
            tickets = tickets.filter(status_id=status_id)
            
        # Apply ordering
        tickets = tickets.order_by('-updated_on')  # Most recently updated first
        
        # Serialize the tickets
        # serializer = TicketSerializer(tickets, many=True)
        serializer = EnhancedTicketSerializer(tickets, many=True)
        
        return Response({
            'user_id': target_user.id,
            'username': target_user.username,
            'name': target_user.name if hasattr(target_user, 'name') else target_user.get_full_name(),
            'total_tickets': tickets.count(),
            'tickets': serializer.data
        })
    
class UserPartnersView(APIView):
    """
    View for managing partners (companies) for a specific user.
    Allows listing, adding, removing, and replacing partner companies of a user.
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated, IsSupervisorOrHigher]
    
    def get(self, request, user_id=None):
        """
        Get partners for a specific user or the current user if no ID provided
        """
        if user_id is None:
            # Use current authenticated user
            user = request.user
        else:
            # # Check if user has permission to view another user's partners
            # if not (request.user.is_staff or request.user.is_superuser) and int(user_id) != request.user.id:
            #     return Response({
            #         'error': 'You do not have permission to view this user\'s partners'
            #     }, status=status.HTTP_403_FORBIDDEN)
                
            user = get_object_or_404(User, id=user_id)
        
        # Get all partners for the user
        partners = user.partners.all()
        serializer = UserCompanySerializer(partners, many=True)
        
        return Response({
            'user_id': user.id,
            'username': user.username,
            'partners': serializer.data
        })
    
    def post(self, request, user_id=None):
        """
        Add partners to a specific user or the current user if no ID provided
        """
        if user_id is None:
            # Use current authenticated user
            user = request.user
        else:
            # # Check if user has permission to modify another user's partners
            # if not (request.user.is_staff or request.user.is_superuser):
            #     return Response({
            #         'error': 'You do not have permission to modify this user\'s partners'
            #     }, status=status.HTTP_403_FORBIDDEN)
                
            user = get_object_or_404(User, id=user_id)
        
        # Get partner IDs from request
        partner_ids = request.data.get('partner_ids', [])
        if not partner_ids:
            return Response({
                'error': 'Partner IDs are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate and add partners
        companies = []
        invalid_ids = []
        
        for partner_id in partner_ids:
            try:
                company = Company.objects.get(id=partner_id)
                companies.append(company)
            except Company.DoesNotExist:
                invalid_ids.append(partner_id)
        
        if invalid_ids:
            return Response({
                'error': f'Companies with IDs {invalid_ids} not found',
                'valid_companies': UserCompanySerializer(companies, many=True).data
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Add companies to user's partners
        user.partners.add(*companies)
        
        # Get updated list of partners
        updated_partners = user.partners.all()
        serializer = UserCompanySerializer(updated_partners, many=True)
        
        return Response({
            'message': 'Partners added successfully',
            'user_id': user.id,
            'username': user.username,
            'partners': serializer.data
        })
    
    def put(self, request, user_id=None):
        """
        Replace all partners for a specific user or the current user if no ID provided
        This will remove all existing partners and add only the ones specified in the request
        """
        if user_id is None:
            # Use current authenticated user
            user = request.user
        else:
            # # Check if user has permission to modify another user's partners
            # if not (request.user.is_staff or request.user.is_superuser):
            #     return Response({
            #         'error': 'You do not have permission to modify this user\'s partners'
            #     }, status=status.HTTP_403_FORBIDDEN)
                
            user = get_object_or_404(User, id=user_id)
        
        # Get partner IDs from request
        partner_ids = request.data.get('partner_ids', [])
        if partner_ids is None:  # Allow empty list to clear partners
            return Response({
                'error': 'Partner IDs field is required (can be an empty list)'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate partner IDs
        companies = []
        invalid_ids = []
        
        for partner_id in partner_ids:
            try:
                company = Company.objects.get(id=partner_id)
                companies.append(company)
            except Company.DoesNotExist:
                invalid_ids.append(partner_id)
        
        if invalid_ids:
            return Response({
                'error': f'Companies with IDs {invalid_ids} not found',
                'valid_companies': UserCompanySerializer(companies, many=True).data
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get previous partners for the response
        previous_partners = list(user.partners.all())
        previous_partner_ids = [partner.id for partner in previous_partners]
        
        # Clear all existing partners
        user.partners.clear()
        
        # Add the new partners
        if companies:
            user.partners.add(*companies)
        
        # Get updated list of partners
        updated_partners = user.partners.all()
        serializer = UserCompanySerializer(updated_partners, many=True)
        
        return Response({
            'message': 'Partners updated successfully',
            'user_id': user.id,
            'username': user.username,
            'previous_partner_ids': previous_partner_ids,
            'new_partner_ids': partner_ids,
            'partners': serializer.data
        })
    
    def delete(self, request, user_id=None):
        """
        Remove partners from a specific user or the current user if no ID provided
        """
        if user_id is None:
            # Use current authenticated user
            user = request.user
        else:
            # # Check if user has permission to modify another user's partners
            # if not (request.user.is_staff or request.user.is_superuser):
            #     return Response({
            #         'error': 'You do not have permission to modify this user\'s partners'
            #     }, status=status.HTTP_403_FORBIDDEN)
                
            user = get_object_or_404(User, id=user_id)
        
        # Get partner IDs from request
        partner_ids = request.data.get('partner_ids', [])
        if not partner_ids:
            return Response({
                'error': 'Partner IDs are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate and remove partners
        companies = []
        invalid_ids = []
        
        for partner_id in partner_ids:
            try:
                company = Company.objects.get(id=partner_id)
                companies.append(company)
            except Company.DoesNotExist:
                invalid_ids.append(partner_id)
        
        if invalid_ids:
            return Response({
                'error': f'Companies with IDs {invalid_ids} not found',
                'valid_companies': UserCompanySerializer(companies, many=True).data
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Remove companies from user's partners
        user.partners.remove(*companies)
        
        # Get updated list of partners
        updated_partners = user.partners.all()
        serializer = UserCompanySerializer(updated_partners, many=True)
        
        return Response({
            'message': 'Partners removed successfully',
            'user_id': user.id,
            'username': user.username,
            'partners': serializer.data
        })
    
class UserRolesView(APIView):
    """
    View for managing roles for a specific user.
    Allows listing, adding, removing, and replacing roles of a user.
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated, IsSupervisorOrHigher]
    
    def get(self, request, user_id=None):
        """
        Get roles for a specific user or the current user if no ID provided
        """
        if user_id is None:
            # Use current authenticated user
            user = request.user
        else:
            # # Check if user has permission to view another user's roles
            # if not (request.user.is_staff or request.user.is_superuser) and int(user_id) != request.user.id:
            #     return Response({
            #         'error': 'You do not have permission to view this user\'s roles'
            #     }, status=status.HTTP_403_FORBIDDEN)
                
            user = get_object_or_404(User, id=user_id)
        
        # Get all roles for the user
        user_roles = UserRole.objects.filter(user_id=user)
        serializer = UserRoleSerializer(user_roles, many=True)
        
        # Extract role names for easier reading
        role_names = [user_role.role_id.name for user_role in user_roles]
        
        return Response({
            'user_id': user.id,
            'username': user.username,
            'roles': serializer.data,
            'role_names': role_names
        })
    
    def post(self, request, user_id=None):
        """
        Add roles to a specific user or the current user if no ID provided
        """
        # # Only admins can modify roles
        # if not (request.user.is_staff or request.user.is_superuser):
        #     return Response({
        #         'error': 'You do not have permission to modify roles'
        #     }, status=status.HTTP_403_FORBIDDEN)
        
        if user_id is None:
            # Use current authenticated user
            user = request.user
        else:
            user = get_object_or_404(User, id=user_id)
        
        # Get role IDs from request
        role_ids = request.data.get('role_ids', [])
        if not role_ids:
            return Response({
                'error': 'Role IDs are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate and add roles
        roles = []
        invalid_ids = []
        
        for role_id in role_ids:
            try:
                role = Role.objects.get(role_id=role_id, is_active=True)
                roles.append(role)
            except Role.DoesNotExist:
                invalid_ids.append(role_id)
        
        if invalid_ids:
            return Response({
                'error': f'Roles with IDs {invalid_ids} not found or are inactive'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Add roles to user
        for role in roles:
            UserRole.objects.get_or_create(
                user_id=user,
                role_id=role,
                defaults={'created_by': request.user}
            )
        
        # Get updated list of roles
        updated_user_roles = UserRole.objects.filter(user_id=user)
        serializer = UserRoleSerializer(updated_user_roles, many=True)
        
        # Extract role names for easier reading
        role_names = [user_role.role_id.name for user_role in updated_user_roles]
        
        return Response({
            'message': 'Roles added successfully',
            'user_id': user.id,
            'username': user.username,
            'roles': serializer.data,
            'role_names': role_names
        })
    
    def put(self, request, user_id=None):
        """
        Replace all roles for a specific user or the current user if no ID provided
        This will remove all existing roles and add only the ones specified in the request
        """
        # # Only admins can modify roles
        # if not (request.user.is_staff or request.user.is_superuser):
        #     return Response({
        #         'error': 'You do not have permission to modify roles'
        #     }, status=status.HTTP_403_FORBIDDEN)
        
        if user_id is None:
            # Use current authenticated user
            user = request.user
        else:
            user = get_object_or_404(User, id=user_id)
        
        # Get role IDs from request
        role_ids = request.data.get('role_ids', [])
        if role_ids is None:  # Allow empty list to clear roles
            return Response({
                'error': 'Role IDs field is required (can be an empty list)'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate role IDs
        roles = []
        invalid_ids = []
        
        for role_id in role_ids:
            try:
                role = Role.objects.get(role_id=role_id, is_active=True)
                roles.append(role)
            except Role.DoesNotExist:
                invalid_ids.append(role_id)
        
        if invalid_ids:
            return Response({
                'error': f'Roles with IDs {invalid_ids} not found or are inactive'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get previous roles for the response
        previous_user_roles = list(UserRole.objects.filter(user_id=user))
        previous_role_ids = [user_role.role_id.role_id for user_role in previous_user_roles]
        previous_role_names = [user_role.role_id.name for user_role in previous_user_roles]
        
        # Begin transaction to ensure atomic operation
        with transaction.atomic():
            # Delete all existing roles
            UserRole.objects.filter(user_id=user).delete()
            
            # Add the new roles
            for role in roles:
                UserRole.objects.create(
                    user_id=user,
                    role_id=role,
                    created_by=request.user
                )
        
        # Get updated list of roles
        updated_user_roles = UserRole.objects.filter(user_id=user)
        serializer = UserRoleSerializer(updated_user_roles, many=True)
        
        # Extract role names for easier reading
        role_names = [user_role.role_id.name for user_role in updated_user_roles]
        
        return Response({
            'message': 'Roles updated successfully',
            'user_id': user.id,
            'username': user.username,
            'previous_role_ids': previous_role_ids,
            'previous_role_names': previous_role_names,
            'new_role_ids': role_ids,
            'new_role_names': role_names,
            'roles': serializer.data
        })
    
    def delete(self, request, user_id=None):
        """
        Remove roles from a specific user or the current user if no ID provided
        """
        # # Only admins can modify roles
        # if not (request.user.is_staff or request.user.is_superuser):
        #     return Response({
        #         'error': 'You do not have permission to modify roles'
        #     }, status=status.HTTP_403_FORBIDDEN)
        
        if user_id is None:
            # Use current authenticated user
            user = request.user
        else:
            user = get_object_or_404(User, id=user_id)
        
        # Get role IDs from request
        role_ids = request.data.get('role_ids', [])
        if not role_ids:
            return Response({
                'error': 'Role IDs are required'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validate and remove roles
        roles = []
        invalid_ids = []
        not_assigned_ids = []
        
        for role_id in role_ids:
            try:
                role = Role.objects.get(role_id=role_id)
                roles.append(role)
                
                # Check if user has this role
                if not UserRole.objects.filter(user_id=user, role_id=role).exists():
                    not_assigned_ids.append(role_id)
                
            except Role.DoesNotExist:
                invalid_ids.append(role_id)
        
        if invalid_ids:
            return Response({
                'error': f'Roles with IDs {invalid_ids} not found'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not_assigned_ids:
            return Response({
                'error': f'User does not have roles with IDs {not_assigned_ids}',
                'message': 'No roles were removed'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Remove roles from user
        for role in roles:
            UserRole.objects.filter(user_id=user, role_id=role).delete()
        
        # Get updated list of roles
        updated_user_roles = UserRole.objects.filter(user_id=user)
        serializer = UserRoleSerializer(updated_user_roles, many=True)
        
        # Extract role names for easier reading
        role_names = [user_role.role_id.name for user_role in updated_user_roles]
        
        return Response({
            'message': 'Roles removed successfully',
            'user_id': user.id,
            'username': user.username,
            'roles': serializer.data,
            'role_names': role_names
        })
    
class UserLineAccountUpdateView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated, IsSupervisorOrHigher]

    # def post(self, request, user_id):
    def put(self, request, user_id):
        """
        Update a user's LINE account
        
        Args:
            user_id: ID of the user to update
            
        Request body:
            line_user_id: ID of the LineUserProfile to associate with the user
        """
        try:
            # Verify user exists
            user = get_object_or_404(User, id=user_id)
            
            # # Check if current user has permission to update this user
            # if not request.user.is_superuser and request.user.id != user_id:
            #     return Response({
            #         'error': 'You do not have permission to update this user'
            #     }, status=status.HTTP_403_FORBIDDEN)
            
            # Validate input
            serializer = UserLineAccountUpdateSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
            # Get LINE profile
            line_profile = LineUserProfile.objects.get(line_user_id=serializer.validated_data['line_user_id'])
            
            # Update user's LINE account
            user.line_user_id = line_profile
            user.save()
            
            return Response({
                'message': 'User LINE account updated successfully',
                'user_id': user.id,
                'username': user.username,
                'line_user_id': line_profile.line_user_id,
                'line_display_name': line_profile.display_name
            }, status=status.HTTP_200_OK)
            
        except LineUserProfile.DoesNotExist:
            return Response({
                'error': 'LINE user profile not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'error': f'Error updating user LINE account: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
class DepartmentListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):
    serializer_class = DepartmentSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = Department.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['id']
    ordering = ['id']

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(created_by=user)
        return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request: Request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "Department Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class DepartmentRetrieveUpdateDeleteView(
    LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
):
    serializer_class = DepartmentSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = Department.objects.all()

    def perform_update(self, serializer):
        user = self.request.user
        serializer.save(updated_by=user)
        return super().perform_update(serializer)

    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            response = {"message": "Department Updated Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_200_OK)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request: Request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "Department Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)
    
class UserDepartmentsView(APIView):
    """
    API view for managing a user's departments
    GET: Retrieve a list of departments assigned to a user
    POST: Assign departments to a user
    PUT: Update a user's departments
    DELETE: Remove departments from a user
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, user_id):
        """Get departments assigned to a specific user"""
        try:
            user = get_object_or_404(User, id=user_id)
            departments = user.departments.all()
            serializer = UserDepartmentSerializer(departments, many=True)
            
            return Response({
                'user_id': user_id,
                'departments': serializer.data,
                'total_departments': departments.count()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error fetching user departments: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
    
    def post(self, request, user_id):
        """Assign departments to a user"""
        try:
            user = get_object_or_404(User, id=user_id)
            department_ids = request.data.get('department_ids', [])
            
            if not department_ids:
                return Response({
                    'error': 'No department_ids provided'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate department IDs
            departments = Department.objects.filter(id__in=department_ids)
            if len(departments) != len(department_ids):
                missing_ids = set(department_ids) - set(departments.values_list('id', flat=True))
                return Response({
                    'error': f'Departments with IDs {missing_ids} not found'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Add departments to user
            user.departments.add(*departments)
            
            # Return updated departments
            updated_departments = user.departments.all()
            serializer = UserDepartmentSerializer(updated_departments, many=True)
            
            return Response({
                'message': 'Departments assigned successfully',
                'user_id': user_id,
                'departments': serializer.data,
                'total_departments': updated_departments.count()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error assigning departments: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
    
    def put(self, request, user_id):
        """Replace all departments for a user"""
        try:
            user = get_object_or_404(User, id=user_id)
            department_ids = request.data.get('department_ids', [])
            
            # Validate department IDs (if any provided)
            if department_ids:
                departments = Department.objects.filter(id__in=department_ids)
                if len(departments) != len(department_ids):
                    missing_ids = set(department_ids) - set(departments.values_list('id', flat=True))
                    return Response({
                        'error': f'Departments with IDs {missing_ids} not found'
                    }, status=status.HTTP_404_NOT_FOUND)
                
                # Clear existing departments and set new ones
                user.departments.clear()
                user.departments.add(*departments)
            else:
                # If no departments provided, clear all
                user.departments.clear()
            
            # Return updated departments
            updated_departments = user.departments.all()
            serializer = UserDepartmentSerializer(updated_departments, many=True)
            
            return Response({
                'message': 'Departments updated successfully',
                'user_id': user_id,
                'departments': serializer.data,
                'total_departments': updated_departments.count()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error updating departments: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, user_id):
        """Remove departments from a user"""
        try:
            user = get_object_or_404(User, id=user_id)
            department_ids = request.data.get('department_ids', [])
            
            if not department_ids:
                return Response({
                    'error': 'No department_ids provided'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate department IDs
            departments = Department.objects.filter(id__in=department_ids)
            if not departments.exists():
                return Response({
                    'error': 'No valid departments to remove'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Remove departments from user
            user.departments.remove(*departments)
            
            # Return remaining departments
            remaining_departments = user.departments.all()
            serializer = UserDepartmentSerializer(remaining_departments, many=True)
            
            return Response({
                'message': 'Departments removed successfully',
                'user_id': user_id,
                'departments': serializer.data,
                'total_departments': remaining_departments.count()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error removing departments: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
class UserTagListCreateView(
    LoggingMixin, generics.GenericAPIView, mixins.ListModelMixin, mixins.CreateModelMixin
):
    serializer_class = UserTagSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = UserTag.objects.all()

    filter_backends = [OrderingFilter]
    ordering_fields = ['id']
    ordering = ['id']

    def perform_create(self, serializer):
        user = self.request.user
        serializer.save(created_by=user)
        return super().perform_create(serializer)
    
    def get(self, request: Request, *args, **kwargs):
        return self.list(request, *args, **kwargs)

    def post(self, request: Request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            response = {"message": "UserTag Created Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_201_CREATED)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class UserTagRetrieveUpdateDeleteView(
    LoggingMixin, generics.GenericAPIView, mixins.RetrieveModelMixin, mixins.UpdateModelMixin, mixins.DestroyModelMixin,
):
    serializer_class = UserTagSerializer
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdminUser]

    queryset = UserTag.objects.all()

    def perform_update(self, serializer):
        user = self.request.user
        serializer.save(updated_by=user)
        return super().perform_update(serializer)

    def get(self, request: Request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def put(self, request: Request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            response = {"message": "UserTag Updated Successfully", "data": serializer.data}
            return Response(data=response, status=status.HTTP_200_OK)
        return Response(data=serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request: Request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        response = {"message": "UserTag Deleted Successfully"}
        return Response(data=response, status=status.HTTP_200_OK)

class UserTagsView(APIView):
    """
    API view for managing a user's tags
    GET: Retrieve a list of tags assigned to a user
    POST: Assign tags to a user
    PUT: Update a user's tags
    DELETE: Remove tags from a user
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, user_id):
        """Get tags assigned to a specific user"""
        try:
            user = get_object_or_404(User, id=user_id)
            tags = user.user_tags.all()
            serializer = UserTagBasicSerializer(tags, many=True)
            
            return Response({
                'user_id': user_id,
                'tags': serializer.data,
                'total_tags': tags.count()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error fetching user tags: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
    
    def post(self, request, user_id):
        """Assign tags to a user"""
        try:
            user = get_object_or_404(User, id=user_id)
            tag_ids = request.data.get('tag_ids', [])
            
            if not tag_ids:
                return Response({
                    'error': 'No tag_ids provided'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate tag IDs
            tags = UserTag.objects.filter(id__in=tag_ids)
            if len(tags) != len(tag_ids):
                missing_ids = set(tag_ids) - set(tags.values_list('id', flat=True))
                return Response({
                    'error': f'Tags with IDs {missing_ids} not found'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Add tags to user
            user.user_tags.add(*tags)
            
            # Return updated tags
            updated_tags = user.user_tags.all()
            serializer = UserTagBasicSerializer(updated_tags, many=True)
            
            return Response({
                'message': 'Tags assigned successfully',
                'user_id': user_id,
                'tags': serializer.data,
                'total_tags': updated_tags.count()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error assigning tags: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)
    
    def put(self, request, user_id):
        """Replace all tags for a user"""
        try:
            user = get_object_or_404(User, id=user_id)
            tag_ids = request.data.get('tag_ids', [])
            
            # Validate tag IDs (if any provided)
            if tag_ids:
                tags = UserTag.objects.filter(id__in=tag_ids)
                if len(tags) != len(tag_ids):
                    missing_ids = set(tag_ids) - set(tags.values_list('id', flat=True))
                    return Response({
                        'error': f'Tags with IDs {missing_ids} not found'
                    }, status=status.HTTP_404_NOT_FOUND)
                
                # Clear existing tags and set new ones
                user.user_tags.clear()
                user.user_tags.add(*tags)
            else:
                # If no tags provided, clear all
                user.user_tags.clear()
            
            # Return updated tags
            updated_tags = user.user_tags.all()
            serializer = UserTagBasicSerializer(updated_tags, many=True)
            
            return Response({
                'message': 'Tags updated successfully',
                'user_id': user_id,
                'tags': serializer.data,
                'total_tags': updated_tags.count()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error updating tags: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, user_id):
        """Remove tags from a user"""
        try:
            user = get_object_or_404(User, id=user_id)
            tag_ids = request.data.get('tag_ids', [])
            
            if not tag_ids:
                return Response({
                    'error': 'No tag_ids provided'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate tag IDs
            tags = UserTag.objects.filter(id__in=tag_ids)
            if not tags.exists():
                return Response({
                    'error': 'No valid tags to remove'
                }, status=status.HTTP_404_NOT_FOUND)
            
            # Remove tags from user
            user.user_tags.remove(*tags)
            
            # Return remaining tags
            remaining_tags = user.user_tags.all()
            serializer = UserTagBasicSerializer(remaining_tags, many=True)
            
            return Response({
                'message': 'Tags removed successfully',
                'user_id': user_id,
                'tags': serializer.data,
                'total_tags': remaining_tags.count()
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error removing tags: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)


# ========== LIST VIEWS FOR FILTERS ==========
class PartnerListView(generics.ListAPIView):
    """
    Simple list view for all partners/companies to populate filter dropdown
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        """Get all partners for filter dropdown"""
        try:
            partners = Company.objects.all().values('id', 'name', 'code', 'color')
            
            return Response({
                'partners': list(partners),
                'total_count': len(partners)
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error fetching partners: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

class DepartmentListView(generics.ListAPIView):
    """
    Simple list view for all departments to populate filter dropdown
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        """Get all departments for filter dropdown"""
        try:
            departments = Department.objects.filter(is_active=True).values('id', 'name', 'code', 'color')
            
            return Response({
                'departments': list(departments),
                'total_count': len(departments)
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error fetching departments: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

class SpecializedTagListView(generics.ListAPIView):
    """
    Simple list view for all specialized tags to populate filter dropdown
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        """Get all specialized tags for filter dropdown"""
        try:
            # UserTag model doesn't have is_active field, so get all tags
            tags = UserTag.objects.all().values('id', 'name', 'color')
            
            return Response({
                'specialized_tags': list(tags),
                'total_count': len(tags)
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'error': f'Error fetching specialized tags: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

class UserToggleStatusView(APIView):
    """
    Toggle user active/inactive status
    Only accessible by Admin users
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAdmin]
    
    def post(self, request, user_id):
        """
        Toggle user is_active status with validations
        """
        # Get the target user
        target_user = get_object_or_404(User, id=user_id)
        
        # Validate request data
        serializer = UserToggleStatusSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        reason = serializer.validated_data.get('reason', '')
        
        # Store previous state
        previous_state = target_user.is_active
        new_state = not previous_state
        
        # Validation 1: Check if user has System or Admin role
        user_roles = UserRole.objects.filter(
            user_id=target_user
        ).select_related('role_id')
        
        restricted_roles = ['System', 'Admin']
        user_role_names = [ur.role_id.name for ur in user_roles]
        
        if new_state is False:  # Only check when deactivating
            for role_name in restricted_roles:
                if role_name in user_role_names:
                    return Response({
                        'error': f'Cannot deactivate user with {role_name} role',
                        'user_roles': user_role_names,
                        'message': 'Users with System or Admin roles cannot be deactivated'
                    }, status=status.HTTP_400_BAD_REQUEST)
        
        # Validation 2: Check for active tickets (when deactivating)
        if new_state is False:
            # Get active ticket statuses (not closed)
            active_statuses = Status.objects.filter(
                is_active=True
            ).exclude(name__in=['closed', 'default'])
            
            active_tickets_count = Ticket.objects.filter(
                owner_id=target_user,
                status_id__in=active_statuses
            ).count()
            
            if active_tickets_count > 0:
                # Get some ticket IDs for reference
                active_ticket_ids = list(
                    Ticket.objects.filter(
                        owner_id=target_user,
                        status_id__in=active_statuses
                    ).values_list('id', flat=True)[:5]
                )
                
                return Response({
                    'error': 'Cannot deactivate user with active tickets',
                    'active_tickets_count': active_tickets_count,
                    'sample_ticket_ids': active_ticket_ids,
                    'message': f'User has {active_tickets_count} active tickets that must be reassigned or closed first'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        # Perform the status toggle
        target_user.is_active = new_state
        target_user.updated_by = request.user
        target_user.updated_on = timezone.now()
        
        # Update user status based on active state
        if new_state is False:
            target_user.status = User.StatusChoices.OFFLINE
            target_user.last_active = timezone.now()
        
        target_user.save()
        
        # Create activity log
        UserActivityLog.objects.create(
            user=target_user,
            activity_type=(
                UserActivityLog.ActivityType.ACTIVATED 
                if new_state else 
                UserActivityLog.ActivityType.DEACTIVATED
            ),
            previous_value=str(previous_state),
            new_value=str(new_state),
            reason=reason,
            performed_by=request.user,
            metadata={
                'performed_by_username': request.user.username,
                'performed_by_name': request.user.name,
                'target_user_roles': user_role_names,
                'ip_address': request.META.get('REMOTE_ADDR', '')
            }
        )
        
        # Log the action
        logger.info(
            f"User {request.user.username} (ID: {request.user.id}) "
            f"{'activated' if new_state else 'deactivated'} "
            f"user {target_user.username} (ID: {target_user.id})"
        )
        
        # Get updated user data with additional info
        active_tickets_count = 0
        if new_state:  # Only count if user is now active
            active_statuses = Status.objects.filter(
                is_active=True
            ).exclude(name__in=['closed', 'default'])
            
            active_tickets_count = Ticket.objects.filter(
                owner_id=target_user,
                status_id__in=active_statuses
            ).count()
        
        # Prepare response
        target_user.active_tickets_count = active_tickets_count
        response_serializer = UserStatusResponseSerializer(target_user)
        
        return Response({
            'success': True,
            'message': f"User successfully {'activated' if new_state else 'deactivated'}",
            'previous_state': previous_state,
            'new_state': new_state,
            'data': response_serializer.data
        }, status=status.HTTP_200_OK)

